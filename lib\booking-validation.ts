import { calculateEquipmentCapacity, getAvailableTimeSlots } from "./availability";
import { supabase } from "./supabase";
import { BookingFormData, ParticipantInfo, PriceCalculation } from "./types";

export interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

export interface BookingValidationResult extends ValidationResult {
	priceCalculation?: PriceCalculation;
}

/**
 * Validate participant information (simplified - only age validation)
 */
export function validateParticipant(participant: ParticipantInfo): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Age validation
	if (participant.age === undefined || participant.age === null || participant.age < 0 || participant.age > 120) {
		errors.push("L'âge doit être entre 0 et 120 ans");
	}

	// Age-specific warnings
	if (participant.age && participant.age < 3) {
		warnings.push("Les enfants de moins de 3 ans nécessitent une attention particulière");
	}
	if (participant.age && participant.age > 75) {
		warnings.push("Veuillez vous assurer que cette activité convient aux seniors");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate customer information
 */
export function validateCustomerInfo(customerInfo: any): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Required fields
	if (!customerInfo.firstName?.trim()) {
		errors.push("Le prénom du client est requis");
	}
	if (!customerInfo.lastName?.trim()) {
		errors.push("Le nom du client est requis");
	}
	if (!customerInfo.email?.trim()) {
		errors.push("L'email est requis");
	}
	if (!customerInfo.phone?.trim()) {
		errors.push("Le téléphone est requis");
	}

	// Email validation
	if (customerInfo.email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(customerInfo.email)) {
			errors.push("Format d'email invalide");
		}
	}

	// Phone validation (French format)
	if (customerInfo.phone) {
		const phoneRegex = /^(?:\+33|0)[1-9](?:[0-9]{8})$/;
		if (!phoneRegex.test(customerInfo.phone.replace(/\s/g, ""))) {
			warnings.push("Format de téléphone non standard (attendu: format français)");
		}
	}

	// Emergency contact validation
	if (customerInfo.emergencyContactName && !customerInfo.emergencyContactPhone) {
		warnings.push("Numéro de contact d'urgence manquant");
	}
	if (customerInfo.emergencyContactPhone && !customerInfo.emergencyContactName) {
		warnings.push("Nom du contact d'urgence manquant");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Calculate pricing for participants (simplified version using participant count)
 */
export async function calculateBookingPrice(
	serviceId: string,
	participants: { age?: number }[] | number,
	selectedOptions?: string[]
): Promise<PriceCalculation> {
	try {
		// Handle both participant count (number) and participant array
		const participantCount = typeof participants === "number" ? participants : participants.length;

		// Get service base price, fixed_price flag, and pricing tiers
		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("base_price, fixed_price")
			.eq("id", serviceId)
			.single();

		if (serviceError) throw serviceError;

		const { data: pricingTiers, error: tiersError } = await supabase
			.from("pricing_tiers")
			.select("*")
			.eq("service_id", serviceId)
			.eq("is_active", true)
			.order("min_age");

		if (tiersError) throw tiersError;

		// Handle fixed pricing vs per-participant pricing
		let subtotal: number;
		let pricePerParticipant: number;
		let participantPricing: any[];

		if (service.fixed_price) {
			// Fixed pricing: total price doesn't change with participant count
			subtotal = service.base_price;
			pricePerParticipant = service.base_price; // For display purposes

			// Create participant pricing array with fixed total distributed
			participantPricing = Array.from({ length: participantCount }, (_, index) => ({
				participant: { age: 25 }, // Default age for simplified booking
				tier: {
					id: "fixed",
					tier_name: "Prix fixe",
					price: index === 0 ? service.base_price : 0, // Only first participant shows the price
					min_age: 0,
					max_age: null,
					service_id: serviceId,
					is_active: true,
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString(),
				},
				price: index === 0 ? service.base_price : 0, // Only first participant shows the price
			}));
		} else {
			// Per-participant pricing: use base price or adult pricing tier
			pricePerParticipant = service.base_price;
			if (pricingTiers && pricingTiers.length > 0) {
				// Find adult tier (18+ years) or use the highest age tier as default
				const adultTier =
					pricingTiers.find((tier) => tier.min_age >= 18) || pricingTiers[pricingTiers.length - 1]; // Last tier (highest age)
				pricePerParticipant = adultTier.price;
			}

			// Create simplified participant pricing
			const defaultTier =
				pricingTiers && pricingTiers.length > 0
					? pricingTiers.find((tier) => tier.min_age >= 18) || pricingTiers[pricingTiers.length - 1]
					: {
							id: "default",
							tier_name: "Standard",
							price: pricePerParticipant,
							min_age: 0,
							max_age: null,
							service_id: serviceId,
							is_active: true,
							created_at: new Date().toISOString(),
							updated_at: new Date().toISOString(),
					  };

			participantPricing = Array.from({ length: participantCount }, (_, index) => ({
				participant: { age: 25 }, // Default age for simplified booking
				tier: defaultTier,
				price: pricePerParticipant,
			}));

			subtotal = participantCount * pricePerParticipant;
		}

		// Calculate options price
		let optionsPrice = 0;
		if (selectedOptions && selectedOptions.length > 0) {
			// Get service options
			const { data: serviceWithOptions } = await supabase
				.from("services")
				.select("options")
				.eq("id", serviceId)
				.single();

			if (serviceWithOptions?.options) {
				const serviceOptions = Array.isArray(serviceWithOptions.options) ? serviceWithOptions.options : [];
				optionsPrice = serviceOptions
					.filter((option) => selectedOptions.includes(option.id))
					.reduce((sum, option) => {
						// Handle per-participant pricing for options
						const optionPrice = option.per_participant
							? (option.price || 0) * participantCount
							: option.price || 0;
						return sum + optionPrice;
					}, 0);
			}
		}

		const totalWithOptions = subtotal + optionsPrice;

		return {
			participants: participantPricing,
			subtotal,
			discountAmount: 0, // TODO: Implement discount logic
			total: totalWithOptions,
		};
	} catch (error) {
		console.error("Error calculating booking price:", error);
		throw error;
	}
}

/**
 * Validate complete booking data
 */
export async function validateBooking(bookingData: BookingFormData): Promise<BookingValidationResult> {
	const errors: string[] = [];
	const warnings: string[] = [];

	try {
		// Validate service exists and is active
		console.log("=== BASIC VALIDATION DEBUG START ===");
		console.log("Validating service ID:", bookingData.serviceId);

		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("id, name, is_active, min_age, max_age, max_participants")
			.eq("id", bookingData.serviceId)
			.single();

		console.log("Service query result:", { service, serviceError });

		if (serviceError || !service) {
			console.log("Service validation failed:", serviceError);
			errors.push("Service non trouvé");
			return { isValid: false, errors, warnings };
		}

		if (!service.is_active) {
			console.log("Service is not active");
			errors.push("Ce service n'est plus disponible");
			return { isValid: false, errors, warnings };
		}

		console.log("Service validation passed");

		// Validate time slot using dynamic availability
		// Extract date from timeSlotId (format: serviceId|date|time)
		const timeSlotParts = bookingData.timeSlotId.split("|");
		if (timeSlotParts.length < 3) {
			errors.push("Format de créneau horaire invalide");
			return { isValid: false, errors, warnings };
		}

		const date = timeSlotParts[1];
		const time = timeSlotParts[2];

		// Get available time slots for this date
		const availableSlots = await getAvailableTimeSlots(
			bookingData.serviceId,
			date,
			bookingData.participants.length
		);

		// Find the specific time slot
		const timeSlot = availableSlots.find((slot) => {
			const slotTime = new Date(slot.start_time).toLocaleTimeString("fr-FR", {
				hour: "2-digit",
				minute: "2-digit",
				hour12: false,
			}); // Use local time to match timezone fix
			console.log("Comparing slot time (local):", slotTime, "with requested time:", time);
			return slotTime === time;
		});

		if (!timeSlot || !timeSlot.is_available) {
			errors.push("Créneau horaire non disponible");
			return { isValid: false, errors, warnings };
		}

		// Validate participant count
		const participantCount = bookingData.participants?.length || 0;
		if (participantCount === 0) {
			errors.push("Au moins un participant est requis");
			return { isValid: false, errors, warnings };
		}

		if (participantCount > service.max_participants) {
			errors.push(`Nombre maximum de participants dépassé (${service.max_participants})`);
		}

		// For simple bookings, we don't validate individual participant details
		// Just validate the count and basic service restrictions
		console.log(`Validating ${participantCount} participants for service ${service.name}`);

		// If service has age restrictions, add warnings but don't block booking
		if (service.min_age && service.min_age > 0) {
			warnings.push(`Âge minimum recommandé: ${service.min_age} ans`);
		}
		if (service.max_age) {
			warnings.push(`Âge maximum recommandé: ${service.max_age} ans`);
		}

		// Validate customer info
		const customerValidation = validateCustomerInfo(bookingData.customerInfo);
		if (!customerValidation.isValid) {
			errors.push(...customerValidation.errors);
		}
		warnings.push(...customerValidation.warnings);

		// Check equipment capacity
		const capacityCheck = await calculateEquipmentCapacity(
			bookingData.serviceId,
			timeSlot.start_time,
			timeSlot.end_time,
			bookingData.participants.length
		);

		if (!capacityCheck.available) {
			errors.push("Capacité insuffisante pour ce créneau");
		}

		// Calculate pricing
		let priceCalculation: PriceCalculation | undefined;
		if (errors.length === 0) {
			try {
				const participantCount = bookingData.participants?.length || 0;
				priceCalculation = await calculateBookingPrice(bookingData.serviceId, participantCount);
			} catch (priceError) {
				errors.push("Erreur lors du calcul du prix");
				console.error("Price calculation error:", priceError);
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			priceCalculation,
		};
	} catch (error) {
		console.error("Error validating booking:", error);
		return {
			isValid: false,
			errors: ["Erreur lors de la validation de la réservation"],
			warnings,
		};
	}
}

/**
 * Validate booking before creation (final check)
 */
export async function validateBookingForCreation(bookingData: BookingFormData): Promise<ValidationResult> {
	console.log("=== VALIDATION FOR CREATION DEBUG START ===");
	console.log("Validating booking data for creation:", {
		serviceId: bookingData.serviceId,
		timeSlotId: bookingData.timeSlotId,
		participantCount: bookingData.participants?.length,
		customerInfo: bookingData.customerInfo,
	});

	console.log("Running basic validation...");
	const validation = await validateBooking(bookingData);
	console.log("Basic validation result:", {
		isValid: validation.isValid,
		errors: validation.errors,
		warnings: validation.warnings,
	});

	// Additional checks for creation
	const errors = [...validation.errors];
	const warnings = [...validation.warnings];

	// Check if time slot is still available using dynamic availability (race condition protection)
	console.log("Checking time slot availability...");
	const timeSlotParts = bookingData.timeSlotId.split("|");
	console.log("TimeSlot parts:", timeSlotParts);

	if (timeSlotParts.length >= 3) {
		const date = timeSlotParts[1];
		const time = timeSlotParts[2];
		console.log("Extracted date:", date, "time:", time);

		try {
			console.log("Fetching available slots for:", {
				serviceId: bookingData.serviceId,
				date,
				participantCount: bookingData.participants.length,
			});

			const availableSlots = await getAvailableTimeSlots(
				bookingData.serviceId,
				date,
				bookingData.participants.length
			);

			console.log("Available slots found:", availableSlots.length);
			console.log(
				"Available slots:",
				availableSlots.map((slot) => ({
					start_time: slot.start_time,
					is_available: slot.is_available,
					available_capacity: slot.available_capacity,
				}))
			);

			const timeSlot = availableSlots.find((slot) => {
				const slotTime = new Date(slot.start_time).toLocaleTimeString("fr-FR", {
					hour: "2-digit",
					minute: "2-digit",
					hour12: false,
				}); // Use local time to match timezone fix
				console.log("Comparing slot time (local):", slotTime, "with requested time:", time);
				return slotTime === time;
			});

			console.log("Found matching time slot:", timeSlot ? "YES" : "NO");

			if (!timeSlot || !timeSlot.is_available) {
				console.log("Time slot validation failed:", {
					timeSlotFound: !!timeSlot,
					isAvailable: timeSlot?.is_available,
				});
				errors.push("Ce créneau n'est plus disponible");
			} else {
				console.log("Time slot validation passed");
			}
		} catch (error) {
			console.error("Error checking time slot availability:", error);
			errors.push("Erreur lors de la vérification de disponibilité");
		}
	} else {
		console.log("Invalid timeSlot format, parts length:", timeSlotParts.length);
		errors.push("Format de créneau horaire invalide");
	}

	console.log("Final validation result:", {
		isValid: errors.length === 0,
		errors,
		warnings,
	});
	console.log("=== VALIDATION FOR CREATION DEBUG END ===");

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

// Role-based permission system
export type UserRole = "admin" | "manager" | "employee";

export interface PermissionConfig {
	roles: UserRole[];
	description?: string;
}

// Define permissions for different features
export const PERMISSIONS = {
	// User Management
	"users:read": {
		roles: ["admin", "manager"],
		description: "View user list and details",
	},
	"users:write": {
		roles: ["admin"],
		description: "Create, update, and delete users",
	},

	// Employee Management
	"employees:read": {
		roles: ["admin", "manager"],
		description: "View employee list and details",
	},
	"employees:write": {
		roles: ["admin", "manager"],
		description: "Create, update, and delete employees",
	},

	// Reservation Management
	"reservations:read": {
		roles: ["admin", "manager", "employee"],
		description: "View reservations",
	},
	"reservations:write": {
		roles: ["admin", "manager"],
		description: "Create, update, and cancel reservations",
	},

	// Service Management
	"services:read": {
		roles: ["admin", "manager", "employee"],
		description: "View services",
	},
	"services:write": {
		roles: ["admin", "manager"],
		description: "Create, update, and delete services",
	},

	// Equipment Management
	"equipment:read": {
		roles: ["admin", "manager"],
		description: "View equipment",
	},
	"equipment:write": {
		roles: ["admin", "manager"],
		description: "Create, update, and delete equipment",
	},

	// Analytics and Reports
	"analytics:read": {
		roles: ["admin", "manager"],
		description: "View analytics and reports",
	},

	// System Settings
	"settings:read": {
		roles: ["admin"],
		description: "View system settings",
	},
	"settings:write": {
		roles: ["admin"],
		description: "Modify system settings",
	},

	// Customer Management
	"customers:read": {
		roles: ["admin", "manager"],
		description: "View customer information",
	},
	"customers:write": {
		roles: ["admin", "manager"],
		description: "Update customer information",
	},

	// Notification Management
	"notifications:read": {
		roles: ["admin", "manager", "employee"],
		description: "View notifications",
	},
	"notifications:write": {
		roles: ["admin", "manager"],
		description: "Create, update, and delete notifications",
	},

	// Admin Features
	"admin:read": {
		roles: ["admin"],
		description: "View admin-only features like audit logs",
	},
} as const;

export type Permission = keyof typeof PERMISSIONS;

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(userRole: UserRole | null | undefined, permission: Permission): boolean {
	if (!userRole) return false;

	const permissionConfig = PERMISSIONS[permission];
	if (!permissionConfig) return false;

	return permissionConfig.roles.includes(userRole);
}

/**
 * Check if a user role can access any of the given permissions
 */
export function hasAnyPermission(userRole: UserRole | null | undefined, permissions: Permission[]): boolean {
	return permissions.some((permission) => hasPermission(userRole, permission));
}

/**
 * Check if a user role can access all of the given permissions
 */
export function hasAllPermissions(userRole: UserRole | null | undefined, permissions: Permission[]): boolean {
	return permissions.every((permission) => hasPermission(userRole, permission));
}

/**
 * Get all permissions for a user role
 */
export function getUserPermissions(userRole: UserRole): Permission[] {
	return Object.keys(PERMISSIONS).filter((permission) =>
		hasPermission(userRole, permission as Permission)
	) as Permission[];
}

/**
 * Role hierarchy helper - check if user role has at least the required level
 */
export function hasRoleLevel(userRole: UserRole | null | undefined, requiredRole: UserRole): boolean {
	if (!userRole) return false;

	const roleHierarchy: Record<UserRole, number> = {
		employee: 1,
		manager: 2,
		admin: 3,
	};

	return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

/**
 * Get user role from user metadata (for use with Supabase auth)
 */
export function getUserRole(user: any): UserRole | null {
	return user?.user_metadata?.role || null;
}

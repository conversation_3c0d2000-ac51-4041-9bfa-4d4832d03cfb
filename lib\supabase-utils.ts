import { supabaseAdmin } from "./supabase";

/**
 * Utility function to ensure supabaseAdmin is available
 * Throws an error if supabaseAdmin is null
 */
export function ensureSupabaseAdmin() {
	if (!supabaseAdmin) {
		throw new Error("Database connection not available");
	}
	return supabaseAdmin;
}

/**
 * Type guard to check if supabaseAdmin is available
 */
export function isSupabaseAdminAvailable(): supabaseAdmin is NonNullable<typeof supabaseAdmin> {
	return supabaseAdmin !== null;
}

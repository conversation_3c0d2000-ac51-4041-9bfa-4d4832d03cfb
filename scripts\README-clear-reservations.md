# Reservation Data Cleanup Script

## Overview

The `clear-reservations-debug.js` script is a comprehensive tool for clearing all reservation-related data from the Supabase database. It's designed for development and testing purposes.

## ⚠️ Important Warning

**This script permanently deletes data from the database!**
- Only use this in development/testing environments
- Always backup your data before running this script
- This action cannot be undone

## Features

- **Safe deletion order**: Handles foreign key constraints by deleting in the correct order
- **Dry run mode**: Preview what would be deleted without actually deleting
- **Selective deletion**: Target specific tables only
- **Interactive confirmation**: Prompts for confirmation unless forced
- **Comprehensive verification**: Verifies deletion was successful
- **Detailed logging**: Shows progress and results for each table

## Usage

### Basic Usage
```bash
# Interactive mode with confirmation
node scripts/clear-reservations-debug.js

# Show help
node scripts/clear-reservations-debug.js --help
```

### Options

| Option | Description |
|--------|-------------|
| `--help`, `-h` | Show help message and available tables |
| `--dry-run` | Show what would be deleted without deleting |
| `--force` | Skip confirmation prompt |
| `--table <name>` | Clear specific table only |

### Examples

```bash
# Preview what would be deleted
node scripts/clear-reservations-debug.js --dry-run

# Clear all data without confirmation
node scripts/clear-reservations-debug.js --force

# Clear only payments table
node scripts/clear-reservations-debug.js --table payments

# Dry run for specific table
node scripts/clear-reservations-debug.js --dry-run --table refunds
```

## Tables Cleared

The script clears the following tables in order (respecting foreign key constraints):

### Tables that reference reservations (deleted first):
1. **refunds** - Payment refunds
2. **payments** - Payment records
3. **equipment_reservations** - Equipment reservations
4. **customer_feedback** - Customer feedback/reviews
5. **reservation_status_history** - Reservation status changes
6. **notifications** - Reservation notifications
7. **customer_journey_events** - Customer journey tracking

### Main table:
8. **reservations** - Main reservations

### Analytics tables:
9. **customer_analytics** - Customer analytics data
10. **service_analytics** - Service analytics data
11. **employee_analytics** - Employee analytics data
12. **daily_business_metrics** - Daily business metrics
13. **equipment_utilization_history** - Equipment utilization history

## Output

The script provides detailed output including:
- Initial record counts for each table
- Progress during deletion
- Summary of deleted records
- Verification of cleanup
- Error reporting if any issues occur

### Sample Output

```
🚨 RESERVATION DATA CLEANUP SCRIPT
=====================================
⚠️  WARNING: This will delete ALL reservation-related data!
⚠️  This action cannot be undone!

📊 Getting initial data counts...
   📋 refunds: 5 records
   📋 payments: 23 records
   📋 reservations: 15 records
   ...

🗑️  Starting deletion process...
🔄 Clearing refunds (Payment refunds)...
   ✅ Cleared refunds: 5 records deleted
...

📊 CLEANUP SUMMARY
==================
Total records deleted: 43

🔍 Verifying cleanup...
   ✅ refunds: Clean
   ✅ payments: Clean
   ...

🎉 All reservation data successfully cleared!
✨ Cleanup complete!
```

## Prerequisites

- Node.js installed
- Access to Supabase service role key
- `@supabase/supabase-js` package installed

## Safety Features

1. **Confirmation prompt**: Requires typing "yes" to proceed (unless `--force` is used)
2. **Dry run mode**: Test the script without making changes
3. **Error handling**: Graceful error handling with detailed error messages
4. **Verification**: Post-deletion verification to ensure cleanup was successful
5. **Selective targeting**: Option to clear specific tables only

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure you have the correct Supabase service role key
2. **Foreign key constraints**: The script handles this automatically by deleting in the correct order
3. **Network issues**: Check your internet connection and Supabase URL

### Error Messages

- `❌ Error counting/deleting from [table]`: Database connection or permission issue
- `⚠️ Some data may remain`: Some records weren't deleted, check the verification output
- `Table '[name]' not found`: Invalid table name provided with `--table` option

## Development Notes

- The script uses the Supabase service role key for admin access
- Deletion is performed using `.delete().neq('id', '00000000-0000-0000-0000-000000000000')` to delete all records
- The script maintains a list of tables in dependency order to avoid foreign key constraint violations

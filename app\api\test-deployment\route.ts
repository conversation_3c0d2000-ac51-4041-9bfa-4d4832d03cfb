import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/test-deployment - Simple deployment test endpoint
export async function GET(request: NextRequest) {
	try {
		const testData = {
			status: "ok",
			timestamp: new Date().toISOString(),
			environment: process.env.NODE_ENV,
			deployment: "working",
			message: "Admin interface deployment test successful",
			checks: {
				serverRendering: "ok",
				apiRoutes: "ok",
				environmentVariables: {
					NODE_ENV: process.env.NODE_ENV,
					hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
					hasSupabaseAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
					hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
				},
			},
		};

		return NextResponse.json(testData, { status: 200 });
	} catch (error) {
		return NextResponse.json({
			status: "error",
			timestamp: new Date().toISOString(),
			error: error instanceof Error ? error.message : "Unknown error",
			message: "Deployment test failed",
		}, { status: 500 });
	}
}

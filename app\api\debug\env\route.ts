import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// GET /api/debug/env - Debug environment variables (only in development)
export async function GET(request: NextRequest) {
	// Only allow in development
	if (process.env.NODE_ENV !== "development") {
		return NextResponse.json({ error: "Not available in production" }, { status: 404 });
	}

	const envDebug = {
		NODE_ENV: process.env.NODE_ENV,
		NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? "✅ Set" : "❌ Missing",
		NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? "✅ Set" : "❌ Missing",
		SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? "✅ Set" : "❌ Missing",
		NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || "Not set",
		supabaseAdminAvailable: typeof window === "undefined" && process.env.SUPABASE_SERVICE_ROLE_KEY ? "✅ Available" : "❌ Not available",
	};

	return NextResponse.json({
		message: "Environment Debug Info",
		environment: envDebug,
		timestamp: new Date().toISOString(),
	});
}

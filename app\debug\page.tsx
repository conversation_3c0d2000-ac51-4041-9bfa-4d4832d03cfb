import { supabaseAdmin } from "@/lib/supabase";

export default async function DebugPage() {
	let debugInfo: any = {};

	try {
		// Test environment variables
		debugInfo.env = {
			NODE_ENV: process.env.NODE_ENV,
			NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
			NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
			SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
		};

		// Test supabase admin
		debugInfo.supabaseAdmin = {
			available: !!supabaseAdmin,
		};

		// Test database connection
		if (supabaseAdmin) {
			try {
				const { data, error } = await supabaseAdmin.from("profiles").select("count").limit(1);
				debugInfo.database = {
					connected: !error,
					error: error?.message || null,
				};
			} catch (dbError) {
				debugInfo.database = {
					connected: false,
					error: dbError instanceof Error ? dbError.message : "Unknown error",
				};
			}
		}

		debugInfo.status = "success";
	} catch (error) {
		debugInfo.status = "error";
		debugInfo.error = error instanceof Error ? error.message : "Unknown error";
		debugInfo.stack = error instanceof Error ? error.stack : null;
	}

	return (
		<div className="p-8">
			<h1 className="text-2xl font-bold mb-4">Debug Information</h1>
			<pre className="bg-gray-100 p-4 rounded overflow-auto">
				{JSON.stringify(debugInfo, null, 2)}
			</pre>
		</div>
	);
}

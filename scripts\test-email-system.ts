/**
 * Email System Testing Script
 *
 * This script tests all email notification functionality
 * Run with: npx tsx scripts/test-email-system.ts
 */

import {
	sendAdminNewReservationEmail,
	sendAdminPaymentReceivedEmail,
	sendBookingConfirmationEmail,
	sendBookingReminderEmail,
	sendCancellationEmail,
	sendInvoiceEmail,
	sendPaymentConfirmationEmail,
} from "../lib/email-service";
import { generateInvoicePDF } from "../lib/invoice-generator";

// Test configuration
const TEST_CONFIG = {
	customerEmail: "<EMAIL>", // Test customer email
	adminEmails: ["<EMAIL>"], // Test admin email
	customerName: "<PERSON>",
	reservationNumber: "RES-20240123-001",
	serviceName: "Excursion Mangrove",
	date: "2024-01-25",
	time: "09:00",
	participants: 2,
	totalAmount: 150,
	currency: "EUR",
};

async function testBookingConfirmationEmail() {
	console.log("🧪 Testing booking confirmation email...");

	try {
		const result = await sendBookingConfirmationEmail(TEST_CONFIG.customerEmail, TEST_CONFIG.customerName, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			serviceName: TEST_CONFIG.serviceName,
			date: TEST_CONFIG.date,
			time: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			totalAmount: TEST_CONFIG.totalAmount,
			specialRequests: "Test special request",
		});

		if (result.success) {
			console.log("✅ Booking confirmation email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Booking confirmation email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Booking confirmation email error:", error);
	}
}

async function testPaymentConfirmationEmail() {
	console.log("🧪 Testing payment confirmation email...");

	try {
		const result = await sendPaymentConfirmationEmail(TEST_CONFIG.customerEmail, TEST_CONFIG.customerName, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			serviceName: TEST_CONFIG.serviceName,
			date: TEST_CONFIG.date,
			time: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			amount: TEST_CONFIG.totalAmount,
			currency: TEST_CONFIG.currency,
			paymentDate: new Date().toLocaleDateString("fr-FR"),
			isDeposit: false,
		});

		if (result.success) {
			console.log("✅ Payment confirmation email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Payment confirmation email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Payment confirmation email error:", error);
	}
}

async function testCancellationEmail() {
	console.log("🧪 Testing cancellation email...");

	try {
		const result = await sendCancellationEmail(TEST_CONFIG.customerEmail, TEST_CONFIG.customerName, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			serviceName: TEST_CONFIG.serviceName,
			date: TEST_CONFIG.date,
			time: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			totalAmount: TEST_CONFIG.totalAmount,
			refundAmount: TEST_CONFIG.totalAmount,
			cancellationReason: "Test cancellation",
		});

		if (result.success) {
			console.log("✅ Cancellation email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Cancellation email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Cancellation email error:", error);
	}
}

async function testReminderEmail() {
	console.log("🧪 Testing reminder email...");

	try {
		const result = await sendBookingReminderEmail(TEST_CONFIG.customerEmail, TEST_CONFIG.customerName, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			serviceName: TEST_CONFIG.serviceName,
			date: TEST_CONFIG.date,
			time: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			totalAmount: TEST_CONFIG.totalAmount,
		});

		if (result.success) {
			console.log("✅ Reminder email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Reminder email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Reminder email error:", error);
	}
}

async function testAdminNewReservationEmail() {
	console.log("🧪 Testing admin new reservation email...");

	try {
		const result = await sendAdminNewReservationEmail(TEST_CONFIG.adminEmails, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			customerName: TEST_CONFIG.customerName,
			customerEmail: TEST_CONFIG.customerEmail,
			serviceName: TEST_CONFIG.serviceName,
			date: TEST_CONFIG.date,
			time: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			totalAmount: TEST_CONFIG.totalAmount,
			specialRequests: "Test special request",
		});

		if (result.success) {
			console.log("✅ Admin new reservation email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Admin new reservation email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Admin new reservation email error:", error);
	}
}

async function testAdminPaymentReceivedEmail() {
	console.log("🧪 Testing admin payment received email...");

	try {
		const result = await sendAdminPaymentReceivedEmail(TEST_CONFIG.adminEmails, {
			reservationNumber: TEST_CONFIG.reservationNumber,
			customerName: TEST_CONFIG.customerName,
			amount: TEST_CONFIG.totalAmount,
			currency: TEST_CONFIG.currency,
			paymentDate: new Date().toLocaleDateString("fr-FR"),
			isDeposit: false,
		});

		if (result.success) {
			console.log("✅ Admin payment received email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Admin payment received email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Admin payment received email error:", error);
	}
}

async function testInvoiceEmail() {
	console.log("🧪 Testing invoice email...");

	try {
		// Generate test invoice
		const invoiceData = {
			invoiceNumber: "INV-20240123-001",
			invoiceDate: new Date().toLocaleDateString("fr-FR"),
			customerName: TEST_CONFIG.customerName,
			customerEmail: TEST_CONFIG.customerEmail,
			reservationNumber: TEST_CONFIG.reservationNumber,
			serviceName: TEST_CONFIG.serviceName,
			serviceDate: TEST_CONFIG.date,
			serviceTime: TEST_CONFIG.time,
			participants: TEST_CONFIG.participants,
			unitPrice: TEST_CONFIG.totalAmount / TEST_CONFIG.participants,
			totalAmount: TEST_CONFIG.totalAmount,
			currency: "EUR",
			paymentStatus: "paid" as const,
			paymentDate: new Date().toLocaleDateString("fr-FR"),
			paymentMethod: "Carte bancaire",
		};

		const invoicePDF = await generateInvoicePDF(invoiceData);
		const invoiceBuffer = Buffer.from(await invoicePDF.arrayBuffer());

		const result = await sendInvoiceEmail(
			TEST_CONFIG.customerEmail,
			TEST_CONFIG.customerName,
			{
				invoiceNumber: invoiceData.invoiceNumber,
				reservationNumber: TEST_CONFIG.reservationNumber,
				serviceName: TEST_CONFIG.serviceName,
				totalAmount: TEST_CONFIG.totalAmount,
				currency: TEST_CONFIG.currency,
				paymentStatus: "paid",
			},
			invoiceBuffer
		);

		if (result.success) {
			console.log("✅ Invoice email sent successfully");
			console.log(`   Message ID: ${result.messageId}`);
		} else {
			console.log("❌ Invoice email failed");
			console.log(`   Error: ${result.error}`);
		}
	} catch (error) {
		console.log("❌ Invoice email error:", error);
	}
}

async function runAllTests() {
	console.log("🚀 Starting Email System Tests");
	console.log("================================");
	console.log(`Test emails will be sent to: ${TEST_CONFIG.customerEmail}`);
	console.log(`Admin emails will be sent to: ${TEST_CONFIG.adminEmails.join(", ")}`);
	console.log("");

	// Run all tests
	await testBookingConfirmationEmail();
	console.log("");

	await testPaymentConfirmationEmail();
	console.log("");

	await testCancellationEmail();
	console.log("");

	await testReminderEmail();
	console.log("");

	await testAdminNewReservationEmail();
	console.log("");

	await testAdminPaymentReceivedEmail();
	console.log("");

	await testInvoiceEmail();
	console.log("");

	console.log("✅ All email tests completed!");
	console.log("Check your email inbox for test messages.");
}

// Run tests if this script is executed directly
if (require.main === module) {
	runAllTests().catch(console.error);
}

export {
	runAllTests,
	testAdminNewReservationEmail,
	testAdminPaymentReceivedEmail,
	testBookingConfirmationEmail,
	testCancellationEmail,
	testInvoiceEmail,
	testPaymentConfirmationEmail,
	testReminderEmail,
};

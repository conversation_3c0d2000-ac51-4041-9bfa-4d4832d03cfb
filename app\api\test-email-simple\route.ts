import { NextRequest, NextResponse } from "next/server";
import { sendBookingConfirmationEmail } from "@/lib/email-service";

export async function GET(request: NextRequest) {
	console.log("=== SIMPLE EMAIL TEST START ===");
	
	try {
		const result = await sendBookingConfirmationEmail(
			"<EMAIL>", // Your email
			"<PERSON> Viranin",
			{
				reservationNumber: "TEST-123",
				serviceName: "Test Service",
				date: "2025-07-25",
				time: "09:00",
				participants: 2,
				totalAmount: 50,
				specialRequests: "Test booking"
			}
		);

		console.log("Simple email test result:", result);
		console.log("=== SIMPLE EMAIL TEST END ===");

		return NextResponse.json({
			success: true,
			emailResult: result,
			message: result.success ? "Email sent successfully!" : "Em<PERSON> failed to send"
		});

	} catch (error) {
		console.error("Simple email test error:", error);
		console.log("=== SIMPLE EMAIL TEST END (ERROR) ===");
		
		return NextResponse.json({
			success: false,
			error: error instanceof Error ? error.message : "Unknown error",
			message: "Email test failed"
		}, { status: 500 });
	}
}

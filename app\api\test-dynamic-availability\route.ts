import { getAvailableTimeSlots } from "@/lib/availability";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const serviceId = searchParams.get("serviceId");
		const date = searchParams.get("date");
		const participants = parseInt(searchParams.get("participants") || "1");

		if (!serviceId || !date) {
			return NextResponse.json({ error: "serviceId and date parameters are required" }, { status: 400 });
		}

		console.log(
			`Testing dynamic availability for service ${serviceId} on ${date} with ${participants} participants`
		);

		// Test the dynamic availability system
		const timeSlots = await getAvailableTimeSlots(serviceId, date, participants);

		return NextResponse.json({
			success: true,
			message: "Dynamic availability system test",
			data: {
				serviceId,
				date,
				participants,
				availableSlots: timeSlots.length,
				timeSlots: timeSlots.map((slot) => ({
					id: slot.id,
					start_time: slot.start_time,
					end_time: slot.end_time,
					available_capacity: slot.available_capacity,
					is_available: slot.is_available,
				})),
			},
		});
	} catch (error) {
		console.error("Error testing dynamic availability:", error);
		return NextResponse.json(
			{
				error: "Internal server error",
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}

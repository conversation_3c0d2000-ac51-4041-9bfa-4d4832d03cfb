/**
 * Admin email templates for notifications
 */

/**
 * Generate admin new reservation HTML email
 */
export function generateAdminNewReservationHTML(reservationData: any): string {
  const specialRequestsSection = reservationData.specialRequests 
    ? `<div class="special-requests">
        <h3>Demandes spéciales :</h3>
        <p>${reservationData.specialRequests}</p>
      </div>` 
    : '';

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Nouvelle réservation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .alert-badge { background-color: #3b82f6; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .info-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .customer-section { background-color: #e0f2fe; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
        .special-requests { background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Administration</p>
      </div>

      <div class="alert-badge">🔔 Nouvelle Réservation</div>

      <h2>Nouvelle réservation reçue</h2>
      
      <p>Une nouvelle réservation vient d'être créée et nécessite votre attention.</p>

      <div class="info-section">
        <h3>Détails de la réservation :</h3>
        <p><strong>Numéro de réservation :</strong> ${reservationData.reservationNumber}</p>
        <p><strong>Service :</strong> ${reservationData.serviceName}</p>
        <p><strong>Date :</strong> ${reservationData.date}</p>
        <p><strong>Heure :</strong> ${reservationData.time}</p>
        <p><strong>Participants :</strong> ${reservationData.participants}</p>
        <p><strong>Montant total :</strong> ${reservationData.totalAmount}€</p>
      </div>

      <div class="customer-section">
        <h3>Informations client :</h3>
        <p><strong>Nom :</strong> ${reservationData.customerName}</p>
        <p><strong>Email :</strong> ${reservationData.customerEmail}</p>
      </div>

      ${specialRequestsSection}

      <p><strong>Action requise :</strong> Vérifiez les détails de la réservation et confirmez la disponibilité.</p>

      <div class="footer">
        <p>Soleil & Découverte - Administration</p>
        <p>Connectez-vous à l'interface d'administration pour plus de détails</p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate admin new reservation text email
 */
export function generateAdminNewReservationText(reservationData: any): string {
  const specialRequestsText = reservationData.specialRequests 
    ? `\nDEMANDES SPÉCIALES :\n${reservationData.specialRequests}` 
    : '';

  return `
SOLEIL & DÉCOUVERTE - ADMINISTRATION

🔔 NOUVELLE RÉSERVATION

Une nouvelle réservation vient d'être créée et nécessite votre attention.

DÉTAILS DE LA RÉSERVATION :
- Numéro de réservation : ${reservationData.reservationNumber}
- Service : ${reservationData.serviceName}
- Date : ${reservationData.date}
- Heure : ${reservationData.time}
- Participants : ${reservationData.participants}
- Montant total : ${reservationData.totalAmount}€

INFORMATIONS CLIENT :
- Nom : ${reservationData.customerName}
- Email : ${reservationData.customerEmail}${specialRequestsText}

ACTION REQUISE : Vérifiez les détails de la réservation et confirmez la disponibilité.

Soleil & Découverte - Administration
Connectez-vous à l'interface d'administration pour plus de détails
  `.trim();
}

/**
 * Generate admin payment received HTML email
 */
export function generateAdminPaymentReceivedHTML(paymentData: any): string {
  const paymentTypeText = paymentData.isDeposit ? 'Acompte' : 'Paiement complet';

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${paymentTypeText} reçu</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #10b981; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #10b981; margin-bottom: 10px; }
        .success-badge { background-color: #10b981; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold; display: inline-block; margin: 20px 0; }
        .payment-section { background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e5e5; text-align: center; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <p>Administration</p>
      </div>

      <div class="success-badge">💰 ${paymentTypeText} Reçu</div>

      <h2>${paymentTypeText} confirmé</h2>
      
      <p>Un ${paymentData.isDeposit ? 'acompte a' : 'paiement complet a'} été reçu avec succès.</p>

      <div class="payment-section">
        <h3>Détails du paiement :</h3>
        <p><strong>Réservation :</strong> ${paymentData.reservationNumber}</p>
        <p><strong>Client :</strong> ${paymentData.customerName}</p>
        <p><strong>Montant :</strong> ${paymentData.amount}${paymentData.currency}</p>
        <p><strong>Date :</strong> ${paymentData.paymentDate}</p>
        <p><strong>Type :</strong> ${paymentTypeText}</p>
      </div>

      <p><strong>Action :</strong> ${paymentData.isDeposit ? 'La réservation est confirmée avec acompte.' : 'La réservation est entièrement payée.'}</p>

      <div class="footer">
        <p>Soleil & Découverte - Administration</p>
        <p>Connectez-vous à l'interface d'administration pour plus de détails</p>
      </div>
    </body>
    </html>
  `;
}

/**
 * Generate admin payment received text email
 */
export function generateAdminPaymentReceivedText(paymentData: any): string {
  const paymentTypeText = paymentData.isDeposit ? 'ACOMPTE' : 'PAIEMENT COMPLET';

  return `
SOLEIL & DÉCOUVERTE - ADMINISTRATION

💰 ${paymentTypeText} REÇU

Un ${paymentData.isDeposit ? 'acompte a' : 'paiement complet a'} été reçu avec succès.

DÉTAILS DU PAIEMENT :
- Réservation : ${paymentData.reservationNumber}
- Client : ${paymentData.customerName}
- Montant : ${paymentData.amount}${paymentData.currency}
- Date : ${paymentData.paymentDate}
- Type : ${paymentTypeText}

ACTION : ${paymentData.isDeposit ? 'La réservation est confirmée avec acompte.' : 'La réservation est entièrement payée.'}

Soleil & Découverte - Administration
Connectez-vous à l'interface d'administration pour plus de détails
  `.trim();
}

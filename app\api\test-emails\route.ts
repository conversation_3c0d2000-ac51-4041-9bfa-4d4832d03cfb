import { NextRequest, NextResponse } from "next/server";
import { 
  sendBookingConfirmationEmail, 
  sendPaymentConfirmationEmail,
  sendCancellationEmail,
  sendBookingReminderEmail,
  sendAdminNewReservationEmail,
  sendAdminPaymentReceivedEmail,
  sendInvoiceEmail
} from "@/lib/email-service";
import { generateInvoicePDF } from "@/lib/invoice-generator";

// Test configuration
const TEST_CONFIG = {
  customerEmail: "<EMAIL>",
  adminEmails: ["<EMAIL>"],
  customerName: "<PERSON>",
  reservationNumber: "RES-20240123-001",
  serviceName: "Excursion Mangrove",
  date: "2024-01-25",
  time: "09:00",
  participants: 2,
  totalAmount: 150,
  currency: "EUR"
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const emailType = searchParams.get("type");

  if (!emailType) {
    return NextResponse.json({
      error: "Please specify email type",
      availableTypes: [
        "booking-confirmation",
        "payment-confirmation", 
        "cancellation",
        "reminder",
        "admin-new-reservation",
        "admin-payment-received",
        "invoice",
        "all"
      ]
    }, { status: 400 });
  }

  const results: any[] = [];

  try {
    if (emailType === "booking-confirmation" || emailType === "all") {
      console.log("Testing booking confirmation email...");
      const result = await sendBookingConfirmationEmail(
        TEST_CONFIG.customerEmail,
        TEST_CONFIG.customerName,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          serviceName: TEST_CONFIG.serviceName,
          date: TEST_CONFIG.date,
          time: TEST_CONFIG.time,
          participants: TEST_CONFIG.participants,
          totalAmount: TEST_CONFIG.totalAmount,
          specialRequests: "Test special request"
        }
      );
      results.push({ type: "booking-confirmation", ...result });
    }

    if (emailType === "payment-confirmation" || emailType === "all") {
      console.log("Testing payment confirmation email...");
      const result = await sendPaymentConfirmationEmail(
        TEST_CONFIG.customerEmail,
        TEST_CONFIG.customerName,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          serviceName: TEST_CONFIG.serviceName,
          date: TEST_CONFIG.date,
          time: TEST_CONFIG.time,
          participants: TEST_CONFIG.participants,
          amount: TEST_CONFIG.totalAmount,
          currency: TEST_CONFIG.currency,
          paymentDate: new Date().toLocaleDateString("fr-FR"),
          isDeposit: false
        }
      );
      results.push({ type: "payment-confirmation", ...result });
    }

    if (emailType === "cancellation" || emailType === "all") {
      console.log("Testing cancellation email...");
      const result = await sendCancellationEmail(
        TEST_CONFIG.customerEmail,
        TEST_CONFIG.customerName,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          serviceName: TEST_CONFIG.serviceName,
          date: TEST_CONFIG.date,
          time: TEST_CONFIG.time,
          participants: TEST_CONFIG.participants,
          totalAmount: TEST_CONFIG.totalAmount,
          refundAmount: TEST_CONFIG.totalAmount,
          cancellationReason: "Test cancellation"
        }
      );
      results.push({ type: "cancellation", ...result });
    }

    if (emailType === "reminder" || emailType === "all") {
      console.log("Testing reminder email...");
      const result = await sendBookingReminderEmail(
        TEST_CONFIG.customerEmail,
        TEST_CONFIG.customerName,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          serviceName: TEST_CONFIG.serviceName,
          date: TEST_CONFIG.date,
          time: TEST_CONFIG.time,
          participants: TEST_CONFIG.participants,
          totalAmount: TEST_CONFIG.totalAmount
        }
      );
      results.push({ type: "reminder", ...result });
    }

    if (emailType === "admin-new-reservation" || emailType === "all") {
      console.log("Testing admin new reservation email...");
      const result = await sendAdminNewReservationEmail(
        TEST_CONFIG.adminEmails,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          customerName: TEST_CONFIG.customerName,
          customerEmail: TEST_CONFIG.customerEmail,
          serviceName: TEST_CONFIG.serviceName,
          date: TEST_CONFIG.date,
          time: TEST_CONFIG.time,
          participants: TEST_CONFIG.participants,
          totalAmount: TEST_CONFIG.totalAmount,
          specialRequests: "Test special request"
        }
      );
      results.push({ type: "admin-new-reservation", ...result });
    }

    if (emailType === "admin-payment-received" || emailType === "all") {
      console.log("Testing admin payment received email...");
      const result = await sendAdminPaymentReceivedEmail(
        TEST_CONFIG.adminEmails,
        {
          reservationNumber: TEST_CONFIG.reservationNumber,
          customerName: TEST_CONFIG.customerName,
          amount: TEST_CONFIG.totalAmount,
          currency: TEST_CONFIG.currency,
          paymentDate: new Date().toLocaleDateString("fr-FR"),
          isDeposit: false
        }
      );
      results.push({ type: "admin-payment-received", ...result });
    }

    if (emailType === "invoice" || emailType === "all") {
      console.log("Testing invoice email...");
      
      // Generate test invoice
      const invoiceData = {
        invoiceNumber: "INV-20240123-001",
        invoiceDate: new Date().toLocaleDateString("fr-FR"),
        customerName: TEST_CONFIG.customerName,
        customerEmail: TEST_CONFIG.customerEmail,
        reservationNumber: TEST_CONFIG.reservationNumber,
        serviceName: TEST_CONFIG.serviceName,
        serviceDate: TEST_CONFIG.date,
        serviceTime: TEST_CONFIG.time,
        participants: TEST_CONFIG.participants,
        unitPrice: TEST_CONFIG.totalAmount / TEST_CONFIG.participants,
        totalAmount: TEST_CONFIG.totalAmount,
        currency: "EUR",
        paymentStatus: "paid" as const,
        paymentDate: new Date().toLocaleDateString("fr-FR"),
        paymentMethod: "Carte bancaire"
      };

      const invoicePDF = await generateInvoicePDF(invoiceData);
      const invoiceBuffer = Buffer.from(await invoicePDF.arrayBuffer());

      const result = await sendInvoiceEmail(
        TEST_CONFIG.customerEmail,
        TEST_CONFIG.customerName,
        {
          invoiceNumber: invoiceData.invoiceNumber,
          reservationNumber: TEST_CONFIG.reservationNumber,
          serviceName: TEST_CONFIG.serviceName,
          totalAmount: TEST_CONFIG.totalAmount,
          currency: TEST_CONFIG.currency,
          paymentStatus: "paid"
        },
        invoiceBuffer
      );
      results.push({ type: "invoice", ...result });
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      success: true,
      message: `Email test completed for type: ${emailType}`,
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount
      },
      results,
      testConfig: {
        customerEmail: TEST_CONFIG.customerEmail,
        adminEmails: TEST_CONFIG.adminEmails,
        domain: "devprototype-01-adamaitech.xyz"
      }
    });

  } catch (error) {
    console.error("Email test error:", error);
    return NextResponse.json({
      success: false,
      error: "Email test failed",
      details: error instanceof Error ? error.message : "Unknown error",
      results
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { emailType, customerEmail, adminEmails } = body;

    // Override test config if provided
    const config = {
      ...TEST_CONFIG,
      customerEmail: customerEmail || TEST_CONFIG.customerEmail,
      adminEmails: adminEmails || TEST_CONFIG.adminEmails
    };

    // Use the same logic as GET but with custom config
    // This allows testing with different email addresses
    
    return NextResponse.json({
      success: true,
      message: "Custom email test endpoint ready",
      config
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: "Invalid request body"
    }, { status: 400 });
  }
}

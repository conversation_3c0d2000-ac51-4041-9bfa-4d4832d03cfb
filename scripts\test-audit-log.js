#!/usr/bin/env node

/**
 * Test Script: Admin Audit Log
 *
 * This script tests the admin audit log functionality by:
 * 1. Creating a test audit log entry
 * 2. Verifying the entry was created correctly
 * 3. Testing the API endpoint
 */

const { createClient } = require("@supabase/supabase-js");

// Supabase configuration
const SUPABASE_URL = "https://zalzjvuxoffmhaokvzda.supabase.co";
const SUPABASE_SERVICE_KEY =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InphbHpqdnV4b2ZmbWhhb2t2emRhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMDU2MywiZXhwIjoyMDY4MTk2NTYzfQ.3UyRaKx1aoBTDQXJazne99UaoXVa5IfKK5S_oCkwtVI";

async function testAuditLog() {
	console.log("🧪 TESTING ADMIN AUDIT LOG");
	console.log("==========================");

	// Create Supabase client
	const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
		auth: {
			autoRefreshToken: false,
			persistSession: false,
		},
	});

	console.log("🔌 Connected to Supabase");

	try {
		// Step 1: Get an admin user ID for testing
		console.log("\n📋 Step 1: Finding admin user...");
		const { data: adminUsers, error: adminError } = await supabase
			.from("profiles")
			.select("id, email, first_name, last_name")
			.eq("role", "admin")
			.limit(1);

		if (adminError) {
			console.error("❌ Error finding admin user:", adminError.message);
			return;
		}

		if (!adminUsers || adminUsers.length === 0) {
			console.log("⚠️  No admin users found. Creating a test entry with null admin_user_id...");
		}

		const adminUser = adminUsers?.[0];
		console.log(`✅ Found admin user: ${adminUser?.email || "None"}`);

		// Step 2: Create a test audit log entry
		console.log("\n📝 Step 2: Creating test audit log entry...");
		const testEntry = {
			admin_user_id: adminUser?.id || null,
			action: "TEST_ACTION",
			table_name: "test_table",
			record_id: null, // Using null instead of invalid UUID
			old_values: { test: "old_value" },
			new_values: { test: "new_value" },
			ip_address: "127.0.0.1",
			user_agent: "Test Script",
			session_id: "test-session-123",
			created_at: new Date().toISOString(),
		};

		const { data: createdEntry, error: createError } = await supabase
			.from("admin_audit_log")
			.insert(testEntry)
			.select()
			.single();

		if (createError) {
			console.error("❌ Error creating audit log entry:", createError.message);
			return;
		}

		console.log("✅ Test audit log entry created:", createdEntry.id);

		// Step 3: Verify the entry was created correctly
		console.log("\n🔍 Step 3: Verifying audit log entry...");
		const { data: retrievedEntry, error: retrieveError } = await supabase
			.from("admin_audit_log")
			.select("*")
			.eq("id", createdEntry.id)
			.single();

		if (retrieveError) {
			console.error("❌ Error retrieving audit log entry:", retrieveError.message);
			return;
		}

		console.log("✅ Entry retrieved successfully");
		console.log("   - ID:", retrievedEntry.id);
		console.log("   - Action:", retrievedEntry.action);
		console.log("   - Table:", retrievedEntry.table_name);
		console.log("   - Admin User ID:", retrievedEntry.admin_user_id || "None");
		console.log("   - Created:", retrievedEntry.created_at);

		// Step 4: Test the API endpoint structure
		console.log("\n🌐 Step 4: Testing API query structure...");
		const { data: recentEntries, error: queryError } = await supabase
			.from("admin_audit_log")
			.select("*")
			.order("created_at", { ascending: false })
			.limit(5);

		if (queryError) {
			console.error("❌ Error querying audit log:", queryError.message);
			return;
		}

		console.log(`✅ Retrieved ${recentEntries.length} recent entries`);
		recentEntries.forEach((entry, index) => {
			console.log(
				`   ${index + 1}. ${entry.action} on ${entry.table_name} by ${entry.admin_user_id || "Unknown"}`
			);
		});

		// Step 5: Clean up test entry
		console.log("\n🧹 Step 5: Cleaning up test entry...");
		const { error: deleteError } = await supabase.from("admin_audit_log").delete().eq("id", createdEntry.id);

		if (deleteError) {
			console.error("❌ Error deleting test entry:", deleteError.message);
		} else {
			console.log("✅ Test entry cleaned up");
		}

		console.log("\n🎉 AUDIT LOG TEST COMPLETED SUCCESSFULLY!");
		console.log("\nNext steps:");
		console.log("1. Login to admin panel at http://localhost:3001/admin/login");
		console.log("2. Navigate to 'Journal d'audit' in the sidebar");
		console.log("3. Verify the audit log page loads correctly");
		console.log("4. Perform some admin actions to generate real audit entries");
	} catch (error) {
		console.error("❌ Test failed:", error);
	}
}

// Run the test
if (require.main === module) {
	testAuditLog()
		.then(() => {
			console.log("\nTest completed");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Test failed:", error);
			process.exit(1);
		});
}

module.exports = { testAuditLog };
